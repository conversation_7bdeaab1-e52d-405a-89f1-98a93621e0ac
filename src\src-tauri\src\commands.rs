use serde::{Deserialize, Serialize};
use std::collections::HashMap;
use tauri::State;
use tokio::sync::Mutex;
use url::Url;
use regex::Regex;

#[derive(Debug, Serialize, Deserialize, Clone)]
pub struct Video {
    pub id: String,
    pub title: String,
    pub author: String,
    pub duration: String,
    pub thumbnail: String,
    pub url: String,
}

#[derive(Debug, Serialize, Deserialize, Clone)]
pub struct Playlist {
    pub id: String,
    pub title: String,
    pub description: String,
    pub thumbnail: String,
    pub url: String,
    pub videos: Vec<Video>,
    pub video_count: usize,
    pub created_at: String,
    pub updated_at: String,
}

#[derive(Debug, Serialize, Deserialize, Clone)]
pub struct Mix {
    pub id: String,
    pub name: String,
    pub playlists: Vec<String>,
    pub created_at: String,
    pub updated_at: String,
}

pub struct PlaylistStore(pub Mutex<HashMap<String, Playlist>>);
pub struct MixStore(pub Mutex<HashMap<String, Mix>>);

#[tauri::command]
pub async fn add_playlist_by_url(
    url: String,
    playlist_store: State<'_, PlaylistStore>,
) -> Result<Playlist, String> {
    // Validate YouTube playlist URL
    if !is_valid_youtube_playlist_url(&url) {
        return Err("Invalid YouTube playlist URL".to_string());
    }

    // Extract playlist ID from URL
    let playlist_id = extract_playlist_id(&url)?;

    // Fetch playlist data (mock implementation for now)
    let playlist = fetch_youtube_playlist(&playlist_id).await?;

    // Store playlist
    let mut store = playlist_store.0.lock().await;
    store.insert(playlist.id.clone(), playlist.clone());

    Ok(playlist)
}

#[tauri::command]
pub async fn get_playlists(
    playlist_store: State<'_, PlaylistStore>,
) -> Result<Vec<Playlist>, String> {
    let store = playlist_store.0.lock().await;
    Ok(store.values().cloned().collect())
}

#[tauri::command]
pub async fn delete_playlist(
    playlist_id: String,
    playlist_store: State<'_, PlaylistStore>,
) -> Result<(), String> {
    let mut store = playlist_store.0.lock().await;
    store.remove(&playlist_id);
    Ok(())
}

#[tauri::command]
pub async fn refresh_playlist(
    playlist_id: String,
    playlist_store: State<'_, PlaylistStore>,
) -> Result<Playlist, String> {
    let mut store = playlist_store.0.lock().await;

    if let Some(playlist) = store.get(&playlist_id) {
        let url = playlist.url.clone();
        drop(store); // Release lock before async operation

        // Re-fetch playlist data
        let updated_playlist = fetch_youtube_playlist(&playlist_id).await?;

        // Update store
        let mut store = playlist_store.0.lock().await;
        store.insert(playlist_id, updated_playlist.clone());

        Ok(updated_playlist)
    } else {
        Err("Playlist not found".to_string())
    }
}

#[tauri::command]
pub async fn create_mix(
    name: String,
    playlist_ids: Vec<String>,
    mix_store: State<'_, MixStore>,
) -> Result<Mix, String> {
    let mix = Mix {
        id: uuid::Uuid::new_v4().to_string(),
        name,
        playlists: playlist_ids,
        created_at: chrono::Utc::now().to_rfc3339(),
        updated_at: chrono::Utc::now().to_rfc3339(),
    };

    let mut store = mix_store.0.lock().await;
    store.insert(mix.id.clone(), mix.clone());

    Ok(mix)
}

#[tauri::command]
pub async fn get_mixes(
    mix_store: State<'_, MixStore>,
) -> Result<Vec<Mix>, String> {
    let store = mix_store.0.lock().await;
    Ok(store.values().cloned().collect())
}

#[tauri::command]
pub async fn search_videos(
    query: String,
    playlist_store: State<'_, PlaylistStore>,
) -> Result<Vec<Video>, String> {
    let store = playlist_store.0.lock().await;
    let mut results = Vec::new();

    let query_lower = query.to_lowercase();

    for playlist in store.values() {
        for video in &playlist.videos {
            if video.title.to_lowercase().contains(&query_lower)
                || video.author.to_lowercase().contains(&query_lower) {
                results.push(video.clone());
            }
        }
    }

    Ok(results)
}

fn is_valid_youtube_playlist_url(url: &str) -> bool {
    let youtube_regex = Regex::new(r"^https?://(www\.)?(youtube\.com|youtu\.be).*[?&]list=([a-zA-Z0-9_-]+)").unwrap();
    youtube_regex.is_match(url)
}

fn extract_playlist_id(url: &str) -> Result<String, String> {
    let parsed_url = Url::parse(url).map_err(|_| "Invalid URL format")?;

    for (key, value) in parsed_url.query_pairs() {
        if key == "list" {
            return Ok(value.to_string());
        }
    }

    Err("No playlist ID found in URL".to_string())
}

// Mock implementation - in a real app, this would use YouTube Data API
async fn fetch_youtube_playlist(playlist_id: &str) -> Result<Playlist, String> {
    // This is a mock implementation
    // In a real app, you would use the YouTube Data API
    let mock_videos = vec![
        Video {
            id: "dQw4w9WgXcQ".to_string(),
            title: "Rick Astley - Never Gonna Give You Up".to_string(),
            author: "Rick Astley".to_string(),
            duration: "3:33".to_string(),
            thumbnail: "https://img.youtube.com/vi/dQw4w9WgXcQ/maxresdefault.jpg".to_string(),
            url: "https://www.youtube.com/watch?v=dQw4w9WgXcQ".to_string(),
        },
    ];

    Ok(Playlist {
        id: playlist_id.to_string(),
        title: "Sample Playlist".to_string(),
        description: "A sample playlist for testing".to_string(),
        thumbnail: "https://img.youtube.com/vi/dQw4w9WgXcQ/maxresdefault.jpg".to_string(),
        url: format!("https://www.youtube.com/playlist?list={}", playlist_id),
        video_count: mock_videos.len(),
        videos: mock_videos,
        created_at: chrono::Utc::now().to_rfc3339(),
        updated_at: chrono::Utc::now().to_rfc3339(),
    })
}
