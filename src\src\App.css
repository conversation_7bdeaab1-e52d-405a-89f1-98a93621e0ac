/* <PERSON> Shuffler App Styles */
.app {
  height: 100vh;
  background: var(--bg-primary);
  overflow: hidden;
}

.app-container {
  height: 100%;
  display: flex;
  flex-direction: column;
}

/* Header */
.app-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 16px 24px;
  background: var(--gradient-primary);
  border-bottom: 1px solid var(--border-color);
  backdrop-filter: blur(20px);
  z-index: 100;
}

.header-left {
  display: flex;
  align-items: center;
  gap: 12px;
}

.app-logo {
  width: 32px;
  height: 32px;
  filter: drop-shadow(0 0 8px var(--accent-glow));
}

.app-title {
  font-size: 20px;
  font-weight: 700;
  color: var(--text-primary);
  margin: 0;
  background: var(--gradient-accent);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.header-actions {
  display: flex;
  gap: 12px;
}

/* Main Content */
.app-main {
  flex: 1;
  display: flex;
  height: calc(100vh - 80px);
  overflow: hidden;
}

.player-section {
  flex: 1;
  display: flex;
  flex-direction: column;
  background: var(--bg-secondary);
}