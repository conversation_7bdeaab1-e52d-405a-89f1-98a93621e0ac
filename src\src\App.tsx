import React, { useState, useEffect, useCallback } from 'react';
import { listen } from '@tauri-apps/api/event';
import { motion, AnimatePresence } from 'framer-motion';
import VideoPlayer from './components/VideoPlayer';
import Sidebar from './components/Sidebar';
import SearchBar from './components/SearchBar';
import PlaylistManager from './components/PlaylistManager';
import Controls from './components/Controls';
import HelpOverlay from './components/HelpOverlay';
import { Video, PlayerState, Playlist, Mix } from './types';
import './styles/globals.css';

function App() {
  const [playerState, setPlayerState] = useState<PlayerState>({
    currentVideo: null,
    currentIndex: 0,
    isPlaying: false,
    isMuted: false,
    volume: 80,
    currentTime: 0,
    duration: 0,
    queue: [],
    history: [],
    loopMode: 'off',
    loopCount: 0,
    shuffled: false,
  });

  const [playlists, setPlaylists] = useState<Playlist[]>([]);
  const [mixes, setMixes] = useState<Mix[]>([]);
  const [searchQuery, setSearchQuery] = useState('');
  const [searchResults, setSearchResults] = useState<Video[]>([]);
  const [showPlaylistManager, setShowPlaylistManager] = useState(false);
  const [showHelp, setShowHelp] = useState(false);
  const [isFirstTime, setIsFirstTime] = useState(true);

  // Keyboard shortcuts
  useEffect(() => {
    const handleKeyDown = (event: KeyboardEvent) => {
      // Prevent shortcuts when typing in inputs
      if (event.target instanceof HTMLInputElement || event.target instanceof HTMLTextAreaElement) {
        return;
      }

      switch (event.key.toLowerCase()) {
        case ' ':
        case 'k':
          event.preventDefault();
          togglePlayPause();
          break;
        case 'arrowleft':
          event.preventDefault();
          seekBackward();
          break;
        case 'arrowright':
          event.preventDefault();
          seekForward();
          break;
        case 'm':
          event.preventDefault();
          toggleMute();
          break;
        case 'h':
          event.preventDefault();
          setShowHelp(true);
          break;
        case 'escape':
          event.preventDefault();
          setShowHelp(false);
          setShowPlaylistManager(false);
          break;
      }
    };

    window.addEventListener('keydown', handleKeyDown);
    return () => window.removeEventListener('keydown', handleKeyDown);
  }, []);

  // System tray event listeners
  useEffect(() => {
    const setupTrayListeners = async () => {
      await listen('system-tray-play-pause', () => {
        togglePlayPause();
      });

      await listen('system-tray-next', () => {
        playNext();
      });

      await listen('system-tray-previous', () => {
        playPrevious();
      });
    };

    setupTrayListeners();
  }, []);

  // Show help overlay for first-time users
  useEffect(() => {
    const hasSeenHelp = localStorage.getItem('lightning-shuffler-help-seen');
    if (!hasSeenHelp && isFirstTime) {
      setShowHelp(true);
      setIsFirstTime(false);
    }
  }, [isFirstTime]);

  const togglePlayPause = useCallback(() => {
    setPlayerState(prev => ({ ...prev, isPlaying: !prev.isPlaying }));
  }, []);

  const toggleMute = useCallback(() => {
    setPlayerState(prev => ({ ...prev, isMuted: !prev.isMuted }));
  }, []);

  const seekForward = useCallback(() => {
    setPlayerState(prev => ({
      ...prev,
      currentTime: Math.min(prev.currentTime + 10, prev.duration)
    }));
  }, []);

  const seekBackward = useCallback(() => {
    setPlayerState(prev => ({
      ...prev,
      currentTime: Math.max(prev.currentTime - 10, 0)
    }));
  }, []);

  const playNext = useCallback(() => {
    setPlayerState(prev => {
      if (prev.currentIndex < prev.queue.length - 1) {
        const nextIndex = prev.currentIndex + 1;
        const nextVideo = prev.queue[nextIndex];
        return {
          ...prev,
          currentVideo: nextVideo,
          currentIndex: nextIndex,
          history: [...prev.history, prev.currentVideo].filter(Boolean) as Video[],
        };
      }
      return prev;
    });
  }, []);

  const playPrevious = useCallback(() => {
    setPlayerState(prev => {
      if (prev.history.length > 0) {
        const previousVideo = prev.history[prev.history.length - 1];
        const newHistory = prev.history.slice(0, -1);
        return {
          ...prev,
          currentVideo: previousVideo,
          currentIndex: prev.currentIndex - 1,
          history: newHistory,
          queue: [previousVideo, ...prev.queue.slice(prev.currentIndex)],
        };
      }
      return prev;
    });
  }, []);

  const shuffleQueue = useCallback(() => {
    setPlayerState(prev => {
      const currentVideo = prev.currentVideo;
      const remainingQueue = prev.queue.slice(prev.currentIndex + 1);
      const shuffledQueue = [...remainingQueue].sort(() => Math.random() - 0.5);

      return {
        ...prev,
        queue: currentVideo ? [currentVideo, ...shuffledQueue] : shuffledQueue,
        currentIndex: 0,
        shuffled: !prev.shuffled,
      };
    });
  }, []);

  return (
    <div className="app">
      <motion.div
        className="app-container"
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        transition={{ duration: 0.5 }}
      >
        {/* Header with Search and Playlist Manager */}
        <header className="app-header">
          <div className="header-left">
            <motion.img
              src="/lightning-bolt.png"
              alt="Lightning Shuffler"
              className="app-logo"
              whileHover={{ scale: 1.1, rotate: 5 }}
              transition={{ type: "spring", stiffness: 300 }}
            />
            <h1 className="app-title">Lightning Shuffler</h1>
          </div>

          <SearchBar
            query={searchQuery}
            onQueryChange={setSearchQuery}
            onResults={setSearchResults}
          />

          <div className="header-actions">
            <button
              className="btn btn-primary"
              onClick={() => setShowPlaylistManager(true)}
            >
              Manage Playlists
            </button>
          </div>
        </header>

        {/* Main Content */}
        <main className="app-main">
          {/* Sidebar */}
          <Sidebar
            queue={playerState.queue}
            history={playerState.history}
            currentVideo={playerState.currentVideo}
            searchResults={searchResults}
            searchQuery={searchQuery}
            onVideoSelect={(video, index) => {
              setPlayerState(prev => ({
                ...prev,
                currentVideo: video,
                currentIndex: index,
                isPlaying: true,
              }));
            }}
          />

          {/* Video Player and Controls */}
          <div className="player-section">
            <VideoPlayer
              video={playerState.currentVideo}
              isPlaying={playerState.isPlaying}
              isMuted={playerState.isMuted}
              volume={playerState.volume}
              onTimeUpdate={(currentTime, duration) => {
                setPlayerState(prev => ({ ...prev, currentTime, duration }));
              }}
              onEnded={playNext}
            />

            <Controls
              playerState={playerState}
              onPlayPause={togglePlayPause}
              onNext={playNext}
              onPrevious={playPrevious}
              onShuffle={shuffleQueue}
              onLoopChange={(loopMode, loopCount) => {
                setPlayerState(prev => ({ ...prev, loopMode, loopCount }));
              }}
              onVolumeChange={(volume) => {
                setPlayerState(prev => ({ ...prev, volume }));
              }}
              onMute={toggleMute}
            />
          </div>
        </main>

        {/* Overlays */}
        <AnimatePresence>
          {showPlaylistManager && (
            <PlaylistManager
              playlists={playlists}
              mixes={mixes}
              onClose={() => setShowPlaylistManager(false)}
              onPlaylistsChange={setPlaylists}
              onMixesChange={setMixes}
              onLoadPlaylist={(videos) => {
                setPlayerState(prev => ({
                  ...prev,
                  queue: videos,
                  currentVideo: videos[0] || null,
                  currentIndex: 0,
                }));
              }}
            />
          )}

          {showHelp && (
            <HelpOverlay
              onClose={() => {
                setShowHelp(false);
                localStorage.setItem('lightning-shuffler-help-seen', 'true');
              }}
            />
          )}
        </AnimatePresence>
      </motion.div>
    </div>
  );
}

export default App;
