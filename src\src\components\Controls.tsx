import React, { useState } from 'react';
import { motion } from 'framer-motion';
import { 
  <PERSON>, 
  Pause, 
  Skip<PERSON><PERSON>, 
  Ski<PERSON><PERSON>or<PERSON>, 
  Shuffle, 
  RotateCcw,
  Volume2,
  VolumeX 
} from 'lucide-react';
import { PlayerState } from '../types';

interface ControlsProps {
  playerState: PlayerState;
  onPlayPause: () => void;
  onNext: () => void;
  onPrevious: () => void;
  onShuffle: () => void;
  onLoopChange: (loopMode: 'off' | 'single' | 'playlist', loopCount: number) => void;
  onVolumeChange: (volume: number) => void;
  onMute: () => void;
}

const Controls: React.FC<ControlsProps> = ({
  playerState,
  onPlayPause,
  onNext,
  onPrevious,
  onShuffle,
  onLoopChange,
  onVolumeChange,
  onMute,
}) => {
  const [showVolumeSlider, setShowVolumeSlider] = useState(false);

  const handleLoopClick = (event: React.MouseEvent) => {
    if (event.shiftKey || event.button === 2) {
      // Shift+click or right-click: increase loop count
      event.preventDefault();
      const newCount = playerState.loopCount + 1;
      onLoopChange('single', newCount);
    } else {
      // Regular click: toggle loop mode
      const nextMode = playerState.loopMode === 'off' ? 'single' : 
                      playerState.loopMode === 'single' ? 'playlist' : 'off';
      onLoopChange(nextMode, 0);
    }
  };

  const formatTime = (seconds: number) => {
    const mins = Math.floor(seconds / 60);
    const secs = Math.floor(seconds % 60);
    return `${mins}:${secs.toString().padStart(2, '0')}`;
  };

  const progressPercentage = playerState.duration > 0 
    ? (playerState.currentTime / playerState.duration) * 100 
    : 0;

  return (
    <div className="controls">
      {/* Progress Bar */}
      <div className="progress-section">
        <span className="time-display">
          {formatTime(playerState.currentTime)}
        </span>
        
        <div className="progress-bar">
          <div className="progress-track">
            <motion.div 
              className="progress-fill"
              style={{ width: `${progressPercentage}%` }}
              transition={{ duration: 0.1 }}
            />
          </div>
        </div>
        
        <span className="time-display">
          {formatTime(playerState.duration)}
        </span>
      </div>

      {/* Main Controls */}
      <div className="main-controls">
        {/* Previous */}
        <motion.button
          className="control-btn"
          onClick={onPrevious}
          disabled={playerState.history.length === 0}
          whileHover={{ scale: 1.1 }}
          whileTap={{ scale: 0.95 }}
        >
          <SkipBack size={20} />
        </motion.button>

        {/* Play/Pause */}
        <motion.button
          className="control-btn primary"
          onClick={onPlayPause}
          whileHover={{ scale: 1.1 }}
          whileTap={{ scale: 0.95 }}
        >
          {playerState.isPlaying ? (
            <Pause size={24} fill="currentColor" />
          ) : (
            <Play size={24} fill="currentColor" />
          )}
        </motion.button>

        {/* Next */}
        <motion.button
          className="control-btn"
          onClick={onNext}
          disabled={playerState.currentIndex >= playerState.queue.length - 1}
          whileHover={{ scale: 1.1 }}
          whileTap={{ scale: 0.95 }}
        >
          <SkipForward size={20} />
        </motion.button>

        {/* Shuffle */}
        <motion.button
          className={`control-btn ${playerState.shuffled ? 'active' : ''}`}
          onClick={onShuffle}
          whileHover={{ scale: 1.1 }}
          whileTap={{ scale: 0.95 }}
          animate={playerState.shuffled ? { 
            boxShadow: "0 0 20px var(--accent-glow)" 
          } : {}}
        >
          <Shuffle size={20} />
        </motion.button>

        {/* Loop */}
        <motion.button
          className={`control-btn loop-btn ${playerState.loopMode !== 'off' ? 'active' : ''}`}
          onClick={handleLoopClick}
          onContextMenu={handleLoopClick}
          whileHover={{ scale: 1.1 }}
          whileTap={{ scale: 0.95 }}
          animate={playerState.loopMode !== 'off' ? { 
            boxShadow: "0 0 20px var(--accent-glow)" 
          } : {}}
        >
          <div className="loop-icon-container">
            <RotateCcw size={20} />
            {playerState.loopCount > 0 && (
              <motion.span 
                className="loop-counter"
                initial={{ scale: 0 }}
                animate={{ scale: 1 }}
                key={playerState.loopCount}
              >
                {playerState.loopCount}
              </motion.span>
            )}
          </div>
        </motion.button>
      </div>

      {/* Volume Controls */}
      <div className="volume-controls">
        <motion.button
          className="control-btn volume-btn"
          onClick={onMute}
          onMouseEnter={() => setShowVolumeSlider(true)}
          onMouseLeave={() => setShowVolumeSlider(false)}
          whileHover={{ scale: 1.1 }}
          whileTap={{ scale: 0.95 }}
        >
          {playerState.isMuted || playerState.volume === 0 ? (
            <VolumeX size={20} />
          ) : (
            <Volume2 size={20} />
          )}
        </motion.button>

        <motion.div
          className="volume-slider-container"
          initial={{ opacity: 0, width: 0 }}
          animate={{ 
            opacity: showVolumeSlider ? 1 : 0,
            width: showVolumeSlider ? 100 : 0
          }}
          transition={{ duration: 0.2 }}
          onMouseEnter={() => setShowVolumeSlider(true)}
          onMouseLeave={() => setShowVolumeSlider(false)}
        >
          <input
            type="range"
            min="0"
            max="100"
            value={playerState.volume}
            onChange={(e) => onVolumeChange(Number(e.target.value))}
            className="volume-slider"
          />
        </motion.div>
      </div>
    </div>
  );
};

export default Controls;
