import React from 'react';
import { motion } from 'framer-motion';
import { X, Keyboard, Mouse, Zap } from 'lucide-react';

interface HelpOverlayProps {
  onClose: () => void;
}

const HelpOverlay: React.FC<HelpOverlayProps> = ({ onClose }) => {
  const shortcuts = [
    {
      category: 'Playback',
      icon: <Zap size={20} />,
      items: [
        { key: 'Space / K', description: 'Play/Pause' },
        { key: '← →', description: 'Seek backward/forward 10 seconds' },
        { key: 'M', description: 'Mute/Unmute' },
      ]
    },
    {
      category: 'Navigation',
      icon: <Keyboard size={20} />,
      items: [
        { key: 'H', description: 'Show/Hide this help overlay' },
        { key: 'Escape', description: 'Close overlays' },
      ]
    },
    {
      category: 'Loop Controls',
      icon: <Mouse size={20} />,
      items: [
        { key: 'Click Loop', description: 'Toggle loop mode (Off → Single → Playlist)' },
        { key: 'Shift+Click Loop', description: 'Increase loop counter' },
        { key: 'Right-Click Loop', description: 'Increase loop counter' },
      ]
    }
  ];

  return (
    <motion.div
      className="help-overlay"
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      exit={{ opacity: 0 }}
      transition={{ duration: 0.3 }}
      onClick={onClose}
    >
      <motion.div
        className="help-content"
        initial={{ scale: 0.8, opacity: 0 }}
        animate={{ scale: 1, opacity: 1 }}
        exit={{ scale: 0.8, opacity: 0 }}
        transition={{ duration: 0.3, type: "spring", stiffness: 300 }}
        onClick={(e) => e.stopPropagation()}
      >
        <div className="help-header">
          <div className="help-title-section">
            <motion.img 
              src="/lightning-bolt.png" 
              alt="Lightning Shuffler"
              className="help-logo"
              animate={{ 
                rotate: [0, 5, -5, 0],
                scale: [1, 1.1, 1]
              }}
              transition={{ 
                duration: 2,
                repeat: Infinity,
                ease: "easeInOut"
              }}
            />
            <div>
              <h2>Lightning Shuffler</h2>
              <p>Keyboard Shortcuts & Controls</p>
            </div>
          </div>
          
          <motion.button
            className="close-button"
            onClick={onClose}
            whileHover={{ scale: 1.1 }}
            whileTap={{ scale: 0.95 }}
          >
            <X size={20} />
          </motion.button>
        </div>

        <div className="help-sections">
          {shortcuts.map((section, sectionIndex) => (
            <motion.div
              key={section.category}
              className="help-section"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: sectionIndex * 0.1 + 0.2 }}
            >
              <div className="section-header">
                {section.icon}
                <h3>{section.category}</h3>
              </div>
              
              <div className="shortcuts-list">
                {section.items.map((item, itemIndex) => (
                  <motion.div
                    key={itemIndex}
                    className="shortcut-item"
                    initial={{ opacity: 0, x: -20 }}
                    animate={{ opacity: 1, x: 0 }}
                    transition={{ delay: (sectionIndex * 0.1) + (itemIndex * 0.05) + 0.3 }}
                  >
                    <div className="shortcut-key">
                      {item.key}
                    </div>
                    <div className="shortcut-description">
                      {item.description}
                    </div>
                  </motion.div>
                ))}
              </div>
            </motion.div>
          ))}
        </div>

        <motion.div
          className="help-footer"
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ delay: 0.8 }}
        >
          <div className="footer-content">
            <div className="tip">
              <strong>💡 Pro Tip:</strong> The loop button shows a number when you set a specific loop count. 
              The counter decreases after each loop completion.
            </div>
            
            <div className="welcome-message">
              <p>Welcome to Lightning Shuffler! ⚡</p>
              <p>Add YouTube playlists to get started with your music journey.</p>
            </div>
          </div>
        </motion.div>

        <motion.div
          className="help-actions"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 1 }}
        >
          <button 
            className="btn btn-primary"
            onClick={onClose}
          >
            Got it! Let's start
          </button>
        </motion.div>
      </motion.div>
    </motion.div>
  );
};

export default HelpOverlay;
