import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { 
  X, 
  Plus, 
  Trash2, 
  RefreshCw, 
  Play, 
  List,
  ExternalLink,
  AlertCircle 
} from 'lucide-react';
import { invoke } from '@tauri-apps/api/core';
import { Playlist, Mix, Video } from '../types';

interface PlaylistManagerProps {
  playlists: Playlist[];
  mixes: Mix[];
  onClose: () => void;
  onPlaylistsChange: (playlists: Playlist[]) => void;
  onMixesChange: (mixes: Mix[]) => void;
  onLoadPlaylist: (videos: Video[]) => void;
}

const PlaylistManager: React.FC<PlaylistManagerProps> = ({
  playlists,
  mixes,
  onClose,
  onPlaylistsChange,
  onMixesChange,
  onLoadPlaylist,
}) => {
  const [activeTab, setActiveTab] = useState<'playlists' | 'mixes'>('playlists');
  const [newPlaylistUrl, setNewPlaylistUrl] = useState('');
  const [newMixName, setNewMixName] = useState('');
  const [selectedPlaylists, setSelectedPlaylists] = useState<string[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    loadPlaylists();
    loadMixes();
  }, []);

  const loadPlaylists = async () => {
    try {
      const loadedPlaylists = await invoke<Playlist[]>('get_playlists');
      onPlaylistsChange(loadedPlaylists);
    } catch (error) {
      console.error('Failed to load playlists:', error);
    }
  };

  const loadMixes = async () => {
    try {
      const loadedMixes = await invoke<Mix[]>('get_mixes');
      onMixesChange(loadedMixes);
    } catch (error) {
      console.error('Failed to load mixes:', error);
    }
  };

  const addPlaylist = async () => {
    if (!newPlaylistUrl.trim()) return;

    setIsLoading(true);
    setError(null);

    try {
      const playlist = await invoke<Playlist>('add_playlist_by_url', {
        url: newPlaylistUrl.trim()
      });
      
      onPlaylistsChange([...playlists, playlist]);
      setNewPlaylistUrl('');
    } catch (error) {
      setError(error as string);
    } finally {
      setIsLoading(false);
    }
  };

  const deletePlaylist = async (playlistId: string) => {
    try {
      await invoke('delete_playlist', { playlistId });
      onPlaylistsChange(playlists.filter(p => p.id !== playlistId));
    } catch (error) {
      console.error('Failed to delete playlist:', error);
    }
  };

  const refreshPlaylist = async (playlistId: string) => {
    setIsLoading(true);
    try {
      const updatedPlaylist = await invoke<Playlist>('refresh_playlist', { playlistId });
      onPlaylistsChange(playlists.map(p => p.id === playlistId ? updatedPlaylist : p));
    } catch (error) {
      console.error('Failed to refresh playlist:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const createMix = async () => {
    if (!newMixName.trim() || selectedPlaylists.length === 0) return;

    try {
      const mix = await invoke<Mix>('create_mix', {
        name: newMixName.trim(),
        playlistIds: selectedPlaylists
      });
      
      onMixesChange([...mixes, mix]);
      setNewMixName('');
      setSelectedPlaylists([]);
    } catch (error) {
      console.error('Failed to create mix:', error);
    }
  };

  const loadPlaylistVideos = (playlist: Playlist) => {
    onLoadPlaylist(playlist.videos);
    onClose();
  };

  const loadMixVideos = (mix: Mix) => {
    const mixVideos = mix.playlists.flatMap(playlistId => {
      const playlist = playlists.find(p => p.id === playlistId);
      return playlist ? playlist.videos : [];
    });
    onLoadPlaylist(mixVideos);
    onClose();
  };

  return (
    <motion.div
      className="playlist-manager-overlay"
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      exit={{ opacity: 0 }}
      onClick={onClose}
    >
      <motion.div
        className="playlist-manager"
        initial={{ scale: 0.9, opacity: 0 }}
        animate={{ scale: 1, opacity: 1 }}
        exit={{ scale: 0.9, opacity: 0 }}
        transition={{ type: "spring", stiffness: 300 }}
        onClick={(e) => e.stopPropagation()}
      >
        <div className="manager-header">
          <h2>Playlist Manager</h2>
          <button className="close-button" onClick={onClose}>
            <X size={20} />
          </button>
        </div>

        <div className="manager-tabs">
          <button
            className={`tab ${activeTab === 'playlists' ? 'active' : ''}`}
            onClick={() => setActiveTab('playlists')}
          >
            <List size={16} />
            Playlists ({playlists.length})
          </button>
          <button
            className={`tab ${activeTab === 'mixes' ? 'active' : ''}`}
            onClick={() => setActiveTab('mixes')}
          >
            <Plus size={16} />
            Mixes ({mixes.length})
          </button>
        </div>

        <AnimatePresence mode="wait">
          {activeTab === 'playlists' ? (
            <motion.div
              key="playlists"
              className="tab-content"
              initial={{ opacity: 0, x: -20 }}
              animate={{ opacity: 1, x: 0 }}
              exit={{ opacity: 0, x: 20 }}
            >
              <div className="add-section">
                <div className="input-group">
                  <input
                    type="text"
                    value={newPlaylistUrl}
                    onChange={(e) => setNewPlaylistUrl(e.target.value)}
                    placeholder="Enter YouTube playlist URL..."
                    className="input"
                    onKeyPress={(e) => e.key === 'Enter' && addPlaylist()}
                  />
                  <button
                    className="btn btn-primary"
                    onClick={addPlaylist}
                    disabled={isLoading || !newPlaylistUrl.trim()}
                  >
                    {isLoading ? <RefreshCw className="animate-spin" size={16} /> : <Plus size={16} />}
                    Add Playlist
                  </button>
                </div>
                
                {error && (
                  <motion.div
                    className="error-message"
                    initial={{ opacity: 0, y: -10 }}
                    animate={{ opacity: 1, y: 0 }}
                  >
                    <AlertCircle size={16} />
                    {error}
                  </motion.div>
                )}
              </div>

              <div className="items-list">
                {playlists.map((playlist, index) => (
                  <motion.div
                    key={playlist.id}
                    className="playlist-item"
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ delay: index * 0.05 }}
                  >
                    <div className="item-thumbnail">
                      <img src={playlist.thumbnail} alt={playlist.title} />
                    </div>
                    
                    <div className="item-details">
                      <h4>{playlist.title}</h4>
                      <p>{playlist.videoCount} videos</p>
                      <div className="item-meta">
                        <span>Added {new Date(playlist.createdAt).toLocaleDateString()}</span>
                      </div>
                    </div>

                    <div className="item-actions">
                      <button
                        className="btn btn-icon"
                        onClick={() => loadPlaylistVideos(playlist)}
                        title="Load playlist"
                      >
                        <Play size={16} />
                      </button>
                      
                      <button
                        className="btn btn-icon"
                        onClick={() => refreshPlaylist(playlist.id)}
                        title="Refresh playlist"
                      >
                        <RefreshCw size={16} />
                      </button>
                      
                      <button
                        className="btn btn-icon"
                        onClick={() => window.open(playlist.url, '_blank')}
                        title="Open in YouTube"
                      >
                        <ExternalLink size={16} />
                      </button>
                      
                      <button
                        className="btn btn-icon danger"
                        onClick={() => deletePlaylist(playlist.id)}
                        title="Delete playlist"
                      >
                        <Trash2 size={16} />
                      </button>
                    </div>
                  </motion.div>
                ))}
              </div>
            </motion.div>
          ) : (
            <motion.div
              key="mixes"
              className="tab-content"
              initial={{ opacity: 0, x: 20 }}
              animate={{ opacity: 1, x: 0 }}
              exit={{ opacity: 0, x: -20 }}
            >
              <div className="add-section">
                <div className="input-group">
                  <input
                    type="text"
                    value={newMixName}
                    onChange={(e) => setNewMixName(e.target.value)}
                    placeholder="Mix name..."
                    className="input"
                  />
                  <button
                    className="btn btn-primary"
                    onClick={createMix}
                    disabled={!newMixName.trim() || selectedPlaylists.length === 0}
                  >
                    <Plus size={16} />
                    Create Mix
                  </button>
                </div>

                <div className="playlist-selection">
                  <h4>Select playlists for mix:</h4>
                  <div className="checkbox-list">
                    {playlists.map((playlist) => (
                      <label key={playlist.id} className="checkbox-item">
                        <input
                          type="checkbox"
                          checked={selectedPlaylists.includes(playlist.id)}
                          onChange={(e) => {
                            if (e.target.checked) {
                              setSelectedPlaylists([...selectedPlaylists, playlist.id]);
                            } else {
                              setSelectedPlaylists(selectedPlaylists.filter(id => id !== playlist.id));
                            }
                          }}
                        />
                        <span>{playlist.title}</span>
                      </label>
                    ))}
                  </div>
                </div>
              </div>

              <div className="items-list">
                {mixes.map((mix, index) => (
                  <motion.div
                    key={mix.id}
                    className="mix-item"
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ delay: index * 0.05 }}
                  >
                    <div className="item-details">
                      <h4>{mix.name}</h4>
                      <p>{mix.playlists.length} playlists</p>
                      <div className="mix-playlists">
                        {mix.playlists.map(playlistId => {
                          const playlist = playlists.find(p => p.id === playlistId);
                          return playlist ? (
                            <span key={playlistId} className="mix-playlist-tag">
                              {playlist.title}
                            </span>
                          ) : null;
                        })}
                      </div>
                    </div>

                    <div className="item-actions">
                      <button
                        className="btn btn-icon"
                        onClick={() => loadMixVideos(mix)}
                        title="Load mix"
                      >
                        <Play size={16} />
                      </button>
                    </div>
                  </motion.div>
                ))}
              </div>
            </motion.div>
          )}
        </AnimatePresence>
      </motion.div>
    </motion.div>
  );
};

export default PlaylistManager;
