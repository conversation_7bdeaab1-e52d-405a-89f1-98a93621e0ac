import React, { useState, useEffect, useCallback } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { Search, X } from 'lucide-react';
import { invoke } from '@tauri-apps/api/core';
import { Video } from '../types';

interface SearchBarProps {
  query: string;
  onQueryChange: (query: string) => void;
  onResults: (results: Video[]) => void;
}

const SearchBar: React.FC<SearchBarProps> = ({
  query,
  onQueryChange,
  onResults,
}) => {
  const [isExpanded, setIsExpanded] = useState(false);
  const [isLoading, setIsLoading] = useState(false);

  const performSearch = useCallback(async (searchQuery: string) => {
    if (!searchQuery.trim()) {
      onResults([]);
      return;
    }

    setIsLoading(true);
    try {
      const results = await invoke<Video[]>('search_videos', { 
        query: searchQuery 
      });
      onResults(results);
    } catch (error) {
      console.error('Search failed:', error);
      onResults([]);
    } finally {
      setIsLoading(false);
    }
  }, [onResults]);

  // Debounced search
  useEffect(() => {
    const timeoutId = setTimeout(() => {
      performSearch(query);
    }, 300);

    return () => clearTimeout(timeoutId);
  }, [query, performSearch]);

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    onQueryChange(e.target.value);
  };

  const clearSearch = () => {
    onQueryChange('');
    onResults([]);
    setIsExpanded(false);
  };

  const handleFocus = () => {
    setIsExpanded(true);
  };

  const handleBlur = () => {
    if (!query) {
      setIsExpanded(false);
    }
  };

  return (
    <motion.div 
      className="search-bar"
      animate={{ 
        width: isExpanded ? 400 : 300 
      }}
      transition={{ 
        type: "spring", 
        stiffness: 300, 
        damping: 30 
      }}
    >
      <div className="search-input-container">
        <Search 
          className="search-icon" 
          size={20}
        />
        
        <input
          type="text"
          value={query}
          onChange={handleInputChange}
          onFocus={handleFocus}
          onBlur={handleBlur}
          placeholder="Search videos by title or author..."
          className="search-input"
        />

        <AnimatePresence>
          {(query || isLoading) && (
            <motion.div
              className="search-actions"
              initial={{ opacity: 0, scale: 0.8 }}
              animate={{ opacity: 1, scale: 1 }}
              exit={{ opacity: 0, scale: 0.8 }}
              transition={{ duration: 0.2 }}
            >
              {isLoading ? (
                <div className="loading-indicator animate-spin">⚡</div>
              ) : (
                <button
                  onClick={clearSearch}
                  className="clear-button"
                  type="button"
                >
                  <X size={16} />
                </button>
              )}
            </motion.div>
          )}
        </AnimatePresence>
      </div>

      <AnimatePresence>
        {query && (
          <motion.div
            className="search-highlight"
            initial={{ scaleX: 0 }}
            animate={{ scaleX: 1 }}
            exit={{ scaleX: 0 }}
            transition={{ duration: 0.3 }}
          />
        )}
      </AnimatePresence>
    </motion.div>
  );
};

export default SearchBar;
