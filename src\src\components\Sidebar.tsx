import React, { useMemo } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { Play, Clock, User, Search } from 'lucide-react';
import { Video } from '../types';

interface SidebarProps {
  queue: Video[];
  history: Video[];
  currentVideo: Video | null;
  searchResults: Video[];
  searchQuery: string;
  onVideoSelect: (video: Video, index: number) => void;
}

const Sidebar: React.FC<SidebarProps> = ({
  queue,
  history,
  currentVideo,
  searchResults,
  searchQuery,
  onVideoSelect,
}) => {
  const displayVideos = useMemo(() => {
    if (searchQuery && searchResults.length > 0) {
      return searchResults;
    }
    return [...history.reverse(), ...queue];
  }, [searchQuery, searchResults, history, queue]);

  const isSearchMode = searchQuery && searchResults.length > 0;
  const hasNoResults = searchQuery && searchResults.length === 0;

  const getVideoIndex = (video: Video) => {
    if (isSearchMode) {
      return queue.findIndex(v => v.id === video.id);
    }
    return queue.findIndex(v => v.id === video.id);
  };

  const isCurrentVideo = (video: Video) => {
    return currentVideo?.id === video.id;
  };

  const isInHistory = (video: Video) => {
    return history.some(v => v.id === video.id);
  };

  const highlightText = (text: string, query: string) => {
    if (!query) return text;

    const regex = new RegExp(`(${query})`, 'gi');
    const parts = text.split(regex);

    return parts.map((part, index) =>
      regex.test(part) ? (
        <span key={index} className="highlight">{part}</span>
      ) : (
        part
      )
    );
  };

  return (
    <div className="sidebar">
      <div className="sidebar-header">
        <h3 className="sidebar-title">
          {isSearchMode ? `Search Results (${searchResults.length})` : 'Queue'}
        </h3>
        {!isSearchMode && (
          <div className="queue-stats">
            <span className="stat">
              <Clock size={14} />
              {queue.length} videos
            </span>
          </div>
        )}
      </div>

      <div className="sidebar-content">
        <AnimatePresence mode="wait">
          {hasNoResults ? (
            <motion.div
              key="no-results"
              className="no-results"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: -20 }}
              transition={{ duration: 0.3 }}
            >
              <div className="no-results-content">
                <Search size={48} className="no-results-icon" />
                <h4>No results found</h4>
                <p>Try a different search term or check your spelling</p>
              </div>
            </motion.div>
          ) : (
            <motion.div
              key="video-list"
              className="video-list"
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              exit={{ opacity: 0 }}
              transition={{ duration: 0.3 }}
            >
              {displayVideos.map((video, index) => {
                const videoIndex = getVideoIndex(video);
                const isCurrent = isCurrentVideo(video);
                const inHistory = isInHistory(video);

                return (
                  <motion.div
                    key={`${video.id}-${index}`}
                    className={`video-item ${isCurrent ? 'current' : ''} ${inHistory ? 'history' : ''}`}
                    initial={{ opacity: 0, x: -20 }}
                    animate={{ opacity: 1, x: 0 }}
                    transition={{ delay: index * 0.05 }}
                    onClick={() => onVideoSelect(video, videoIndex)}
                    whileHover={{ scale: 1.02 }}
                    whileTap={{ scale: 0.98 }}
                  >
                    <div className="video-thumbnail">
                      <img
                        src={video.thumbnail}
                        alt={video.title}
                        loading="lazy"
                      />
                      {isCurrent && (
                        <motion.div
                          className="playing-indicator"
                          initial={{ scale: 0 }}
                          animate={{ scale: 1 }}
                          transition={{ type: "spring", stiffness: 300 }}
                        >
                          <Play size={16} fill="currentColor" />
                        </motion.div>
                      )}
                      <div className="video-duration">
                        {video.duration}
                      </div>
                    </div>

                    <div className="video-details">
                      <h4 className="video-title">
                        {highlightText(video.title, searchQuery)}
                      </h4>
                      <div className="video-meta">
                        <span className="video-author">
                          <User size={12} />
                          {highlightText(video.author, searchQuery)}
                        </span>
                      </div>
                    </div>

                    {isCurrent && (
                      <motion.div
                        className="current-glow"
                        initial={{ opacity: 0 }}
                        animate={{ opacity: 1 }}
                        transition={{ duration: 0.5 }}
                      />
                    )}
                  </motion.div>
                );
              })}
            </motion.div>
          )}
        </AnimatePresence>

        {displayVideos.length === 0 && !hasNoResults && (
          <motion.div
            className="empty-queue"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5 }}
          >
            <div className="empty-content">
              <motion.img
                src="/lightning-bolt.png"
                alt="Lightning Shuffler"
                className="empty-logo"
                animate={{
                  scale: [1, 1.05, 1],
                  opacity: [0.7, 1, 0.7]
                }}
                transition={{
                  duration: 2,
                  repeat: Infinity,
                  ease: "easeInOut"
                }}
              />
              <h4>No videos in queue</h4>
              <p>Add a playlist to start listening</p>
            </div>
          </motion.div>
        )}
      </div>
    </div>
  );
};

export default Sidebar;
