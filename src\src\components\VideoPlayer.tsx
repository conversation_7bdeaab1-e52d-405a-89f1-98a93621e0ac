import React, { useEffect, useRef, useState } from 'react';
import { motion } from 'framer-motion';
import { Video } from '../types';

interface VideoPlayerProps {
  video: Video | null;
  isPlaying: boolean;
  isMuted: boolean;
  volume: number;
  onTimeUpdate: (currentTime: number, duration: number) => void;
  onEnded: () => void;
}

const VideoPlayer: React.FC<VideoPlayerProps> = ({
  video,
  isPlaying,
  isMuted,
  volume,
  onTimeUpdate,
  onEnded,
}) => {
  const playerRef = useRef<HTMLDivElement>(null);
  const [player, setPlayer] = useState<any>(null);
  const [isReady, setIsReady] = useState(false);

  // Load YouTube IFrame API
  useEffect(() => {
    if (!window.YT) {
      const tag = document.createElement('script');
      tag.src = 'https://www.youtube.com/iframe_api';
      const firstScriptTag = document.getElementsByTagName('script')[0];
      firstScriptTag.parentNode?.insertBefore(tag, firstScriptTag);

      window.onYouTubeIframeAPIReady = () => {
        setIsReady(true);
      };
    } else {
      setIsReady(true);
    }
  }, []);

  // Initialize player when API is ready and video is available
  useEffect(() => {
    if (isReady && video && playerRef.current && !player) {
      const newPlayer = new window.YT.Player(playerRef.current, {
        height: '100%',
        width: '100%',
        videoId: video.id,
        playerVars: {
          autoplay: 0,
          controls: 0,
          disablekb: 1,
          fs: 0,
          iv_load_policy: 3,
          modestbranding: 1,
          playsinline: 1,
          rel: 0,
        },
        events: {
          onReady: () => {
            setPlayer(newPlayer);
          },
          onStateChange: (event: any) => {
            if (event.data === window.YT.PlayerState.ENDED) {
              onEnded();
            }
          },
        },
      });
    }
  }, [isReady, video, player, onEnded]);

  // Handle play/pause
  useEffect(() => {
    if (player && player.getPlayerState) {
      if (isPlaying) {
        player.playVideo();
      } else {
        player.pauseVideo();
      }
    }
  }, [player, isPlaying]);

  // Handle mute/unmute
  useEffect(() => {
    if (player && player.isMuted !== undefined) {
      if (isMuted) {
        player.mute();
      } else {
        player.unMute();
      }
    }
  }, [player, isMuted]);

  // Handle volume changes
  useEffect(() => {
    if (player && player.setVolume) {
      player.setVolume(volume);
    }
  }, [player, volume]);

  // Update time periodically
  useEffect(() => {
    if (!player || !player.getCurrentTime) return;

    const interval = setInterval(() => {
      const currentTime = player.getCurrentTime();
      const duration = player.getDuration();
      if (currentTime !== undefined && duration !== undefined) {
        onTimeUpdate(currentTime, duration);
      }
    }, 1000);

    return () => clearInterval(interval);
  }, [player, onTimeUpdate]);

  // Load new video when video prop changes
  useEffect(() => {
    if (player && video && player.loadVideoById) {
      player.loadVideoById(video.id);
    }
  }, [player, video]);

  if (!video) {
    return (
      <motion.div 
        className="video-player-placeholder"
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        transition={{ duration: 0.5 }}
      >
        <div className="placeholder-content">
          <motion.img 
            src="/lightning-bolt.png" 
            alt="Lightning Shuffler"
            className="placeholder-logo"
            animate={{ 
              scale: [1, 1.1, 1],
              rotate: [0, 5, -5, 0]
            }}
            transition={{ 
              duration: 2,
              repeat: Infinity,
              ease: "easeInOut"
            }}
          />
          <h2>Welcome to Lightning Shuffler</h2>
          <p>Add a playlist to get started</p>
        </div>
      </motion.div>
    );
  }

  return (
    <motion.div 
      className="video-player"
      initial={{ opacity: 0, scale: 0.95 }}
      animate={{ opacity: 1, scale: 1 }}
      transition={{ duration: 0.3 }}
    >
      <div className="video-container">
        <div ref={playerRef} className="youtube-player" />
        {!isReady && (
          <div className="loading-overlay">
            <div className="loading-spinner animate-spin">⚡</div>
            <p>Loading player...</p>
          </div>
        )}
      </div>
      
      <div className="video-info">
        <motion.h3 
          className="video-title"
          initial={{ y: 20, opacity: 0 }}
          animate={{ y: 0, opacity: 1 }}
          transition={{ delay: 0.2 }}
        >
          {video.title}
        </motion.h3>
        <motion.p 
          className="video-author"
          initial={{ y: 20, opacity: 0 }}
          animate={{ y: 0, opacity: 1 }}
          transition={{ delay: 0.3 }}
        >
          by {video.author}
        </motion.p>
      </div>
    </motion.div>
  );
};

export default VideoPlayer;
