/* Lightning Shuffler Global Styles */
:root {
  /* Dark theme colors */
  --bg-primary: #0a0a0a;
  --bg-secondary: #1a1a1a;
  --bg-tertiary: #2a2a2a;
  --bg-glass: rgba(26, 26, 26, 0.8);

  /* Neon green accent */
  --accent-primary: #00ff88;
  --accent-secondary: #00cc6a;
  --accent-glow: rgba(0, 255, 136, 0.3);

  /* Text colors */
  --text-primary: #ffffff;
  --text-secondary: #b3b3b3;
  --text-muted: #666666;

  /* Border and shadow */
  --border-color: #333333;
  --shadow-primary: 0 4px 20px rgba(0, 0, 0, 0.3);
  --shadow-glow: 0 0 20px var(--accent-glow);

  /* Gradients */
  --gradient-primary: linear-gradient(135deg, var(--bg-secondary), var(--bg-tertiary));
  --gradient-accent: linear-gradient(135deg, var(--accent-primary), var(--accent-secondary));

  /* Transitions */
  --transition-fast: 0.2s ease;
  --transition-medium: 0.3s ease;
  --transition-slow: 0.5s ease;

  /* Border radius */
  --radius-small: 6px;
  --radius-medium: 12px;
  --radius-large: 20px;
}

* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

html,
body {
  height: 100%;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen',
    'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue',
    sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  background: var(--bg-primary);
  color: var(--text-primary);
  overflow: hidden;
}

#root {
  height: 100vh;
  display: flex;
  flex-direction: column;
}

/* Scrollbar styling */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: var(--bg-secondary);
  border-radius: var(--radius-small);
}

::-webkit-scrollbar-thumb {
  background: var(--accent-primary);
  border-radius: var(--radius-small);
  transition: background var(--transition-fast);
}

::-webkit-scrollbar-thumb:hover {
  background: var(--accent-secondary);
}

/* Button base styles */
.btn {
  background: var(--gradient-primary);
  border: 1px solid var(--border-color);
  border-radius: var(--radius-medium);
  color: var(--text-primary);
  cursor: pointer;
  font-size: 14px;
  font-weight: 500;
  padding: 8px 16px;
  transition: all var(--transition-fast);
  display: inline-flex;
  align-items: center;
  gap: 8px;
  text-decoration: none;
  outline: none;
}

.btn:hover {
  background: var(--bg-tertiary);
  border-color: var(--accent-primary);
  box-shadow: var(--shadow-glow);
  transform: translateY(-1px);
}

.btn:active {
  transform: translateY(0);
}

.btn-primary {
  background: var(--gradient-accent);
  border-color: var(--accent-primary);
  color: var(--bg-primary);
  font-weight: 600;
}

.btn-primary:hover {
  background: var(--accent-secondary);
  box-shadow: var(--shadow-glow);
}

.btn-icon {
  padding: 8px;
  border-radius: 50%;
  width: 40px;
  height: 40px;
  justify-content: center;
}

.btn-large {
  padding: 12px 24px;
  font-size: 16px;
}

/* Input styles */
.input {
  background: var(--bg-glass);
  border: 1px solid var(--border-color);
  border-radius: var(--radius-medium);
  color: var(--text-primary);
  font-size: 14px;
  padding: 12px 16px;
  transition: all var(--transition-fast);
  outline: none;
  backdrop-filter: blur(10px);
}

.input:focus {
  border-color: var(--accent-primary);
  box-shadow: var(--shadow-glow);
}

.input::placeholder {
  color: var(--text-muted);
}

/* Card styles */
.card {
  background: var(--gradient-primary);
  border: 1px solid var(--border-color);
  border-radius: var(--radius-large);
  padding: 20px;
  box-shadow: var(--shadow-primary);
  backdrop-filter: blur(10px);
  transition: all var(--transition-medium);
}

.card:hover {
  border-color: var(--accent-primary);
  box-shadow: var(--shadow-glow);
  transform: translateY(-2px);
}

/* Glass effect */
.glass {
  background: var(--bg-glass);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.1);
}

/* Glow effect */
.glow {
  box-shadow: var(--shadow-glow);
}

/* Text utilities */
.text-accent {
  color: var(--accent-primary);
}

.text-muted {
  color: var(--text-muted);
}

.text-secondary {
  color: var(--text-secondary);
}

/* Layout utilities */
.flex {
  display: flex;
}

.flex-col {
  flex-direction: column;
}

.items-center {
  align-items: center;
}

.justify-center {
  justify-content: center;
}

.justify-between {
  justify-content: space-between;
}

.gap-2 {
  gap: 8px;
}

.gap-4 {
  gap: 16px;
}

.gap-6 {
  gap: 24px;
}

/* Animation utilities */
.animate-pulse {
  animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

@keyframes pulse {

  0%,
  100% {
    opacity: 1;
  }

  50% {
    opacity: 0.5;
  }
}

.animate-spin {
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }

  to {
    transform: rotate(360deg);
  }
}

.animate-bounce {
  animation: bounce 1s infinite;
}

@keyframes bounce {

  0%,
  100% {
    transform: translateY(-25%);
    animation-timing-function: cubic-bezier(0.8, 0, 1, 1);
  }

  50% {
    transform: translateY(0);
    animation-timing-function: cubic-bezier(0, 0, 0.2, 1);
  }
}

/* Lightning bolt animation */
@keyframes lightning {
  0% {
    opacity: 0;
    transform: scale(0.8) rotate(-5deg);
  }

  50% {
    opacity: 1;
    transform: scale(1.1) rotate(5deg);
  }

  100% {
    opacity: 0;
    transform: scale(0.8) rotate(-5deg);
  }
}

.animate-lightning {
  animation: lightning 0.6s ease-in-out;
}

/* Search Bar Styles */
.search-bar {
  position: relative;
  display: flex;
  align-items: center;
}

.search-input-container {
  position: relative;
  display: flex;
  align-items: center;
  width: 100%;
  background: var(--bg-glass);
  border: 1px solid var(--border-color);
  border-radius: var(--radius-medium);
  backdrop-filter: blur(10px);
  transition: all var(--transition-fast);
}

.search-input-container:focus-within {
  border-color: var(--accent-primary);
  box-shadow: var(--shadow-glow);
}

.search-icon {
  position: absolute;
  left: 12px;
  color: var(--text-muted);
  z-index: 1;
}

.search-input {
  width: 100%;
  background: transparent;
  border: none;
  padding: 12px 16px 12px 44px;
  color: var(--text-primary);
  font-size: 14px;
  outline: none;
}

.search-input::placeholder {
  color: var(--text-muted);
}

.search-actions {
  position: absolute;
  right: 8px;
  display: flex;
  align-items: center;
  gap: 4px;
}

.clear-button {
  background: none;
  border: none;
  color: var(--text-muted);
  cursor: pointer;
  padding: 4px;
  border-radius: 50%;
  transition: all var(--transition-fast);
}

.clear-button:hover {
  color: var(--accent-primary);
  background: var(--bg-tertiary);
}

.loading-indicator {
  color: var(--accent-primary);
  font-size: 16px;
}

.search-highlight {
  position: absolute;
  bottom: 0;
  left: 0;
  height: 2px;
  background: var(--gradient-accent);
  transform-origin: left;
}

/* Sidebar Styles */
.sidebar {
  width: 350px;
  background: var(--gradient-primary);
  border-right: 1px solid var(--border-color);
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.sidebar-header {
  padding: 16px 20px;
  border-bottom: 1px solid var(--border-color);
  background: var(--bg-glass);
  backdrop-filter: blur(10px);
}

.sidebar-title {
  font-size: 16px;
  font-weight: 600;
  color: var(--text-primary);
  margin: 0 0 8px 0;
}

.queue-stats {
  display: flex;
  gap: 16px;
}

.stat {
  display: flex;
  align-items: center;
  gap: 4px;
  font-size: 12px;
  color: var(--text-secondary);
}

.sidebar-content {
  flex: 1;
  overflow: hidden;
  position: relative;
}

.video-list {
  height: 100%;
  overflow-y: auto;
  padding: 8px;
}

.video-item {
  display: flex;
  gap: 12px;
  padding: 12px;
  border-radius: var(--radius-medium);
  cursor: pointer;
  transition: all var(--transition-fast);
  position: relative;
  margin-bottom: 8px;
  background: var(--bg-secondary);
  border: 1px solid transparent;
}

.video-item:hover {
  background: var(--bg-tertiary);
  border-color: var(--accent-primary);
  transform: translateX(4px);
}

.video-item.current {
  background: var(--bg-tertiary);
  border-color: var(--accent-primary);
  box-shadow: var(--shadow-glow);
}

.video-item.history {
  opacity: 0.6;
}

.video-thumbnail {
  position: relative;
  width: 80px;
  height: 45px;
  border-radius: var(--radius-small);
  overflow: hidden;
  flex-shrink: 0;
}

.video-thumbnail img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.playing-indicator {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  background: var(--accent-primary);
  color: var(--bg-primary);
  border-radius: 50%;
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: var(--shadow-glow);
}

.video-duration {
  position: absolute;
  bottom: 4px;
  right: 4px;
  background: rgba(0, 0, 0, 0.8);
  color: white;
  font-size: 10px;
  padding: 2px 4px;
  border-radius: 2px;
}

.video-details {
  flex: 1;
  min-width: 0;
}

.video-title {
  font-size: 14px;
  font-weight: 500;
  color: var(--text-primary);
  margin: 0 0 4px 0;
  line-height: 1.3;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.video-meta {
  display: flex;
  align-items: center;
  gap: 4px;
}

.video-author {
  font-size: 12px;
  color: var(--text-secondary);
  display: flex;
  align-items: center;
  gap: 4px;
}

.highlight {
  background: var(--accent-primary);
  color: var(--bg-primary);
  padding: 1px 2px;
  border-radius: 2px;
  font-weight: 600;
}

.current-glow {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  border-radius: var(--radius-medium);
  box-shadow: inset 0 0 20px var(--accent-glow);
  pointer-events: none;
}

.no-results,
.empty-queue {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100%;
  padding: 40px 20px;
}

.no-results-content,
.empty-content {
  text-align: center;
}

.no-results-icon,
.empty-logo {
  width: 48px;
  height: 48px;
  color: var(--text-muted);
  margin-bottom: 16px;
}

.empty-logo {
  filter: drop-shadow(0 0 8px var(--accent-glow));
}

.no-results h4,
.empty-content h4 {
  color: var(--text-primary);
  margin: 0 0 8px 0;
  font-size: 18px;
}

.no-results p,
.empty-content p {
  color: var(--text-secondary);
  margin: 0;
  font-size: 14px;
}

/* Video Player Styles */
.video-player {
  flex: 1;
  display: flex;
  flex-direction: column;
  padding: 20px;
  gap: 16px;
}

.video-container {
  position: relative;
  width: 100%;
  height: 0;
  padding-bottom: 56.25%;
  /* 16:9 aspect ratio */
  background: var(--bg-tertiary);
  border-radius: var(--radius-large);
  overflow: hidden;
  box-shadow: var(--shadow-primary);
}

.youtube-player {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
}

.loading-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: var(--bg-tertiary);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: 16px;
  color: var(--text-primary);
}

.loading-spinner {
  font-size: 32px;
  color: var(--accent-primary);
}

.video-player-placeholder {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 40px;
}

.placeholder-content {
  text-align: center;
  max-width: 400px;
}

.placeholder-logo {
  width: 80px;
  height: 80px;
  margin-bottom: 24px;
  filter: drop-shadow(0 0 20px var(--accent-glow));
}

.placeholder-content h2 {
  color: var(--text-primary);
  margin: 0 0 12px 0;
  font-size: 28px;
  background: var(--gradient-accent);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.placeholder-content p {
  color: var(--text-secondary);
  margin: 0;
  font-size: 16px;
}

.video-info {
  text-align: center;
}

.video-title {
  font-size: 24px;
  font-weight: 600;
  color: var(--text-primary);
  margin: 0 0 8px 0;
  line-height: 1.3;
}

.video-author {
  font-size: 16px;
  color: var(--text-secondary);
  margin: 0;
}

/* Controls Styles */
.controls {
  background: var(--gradient-primary);
  border-top: 1px solid var(--border-color);
  padding: 20px 24px;
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.progress-section {
  display: flex;
  align-items: center;
  gap: 12px;
}

.time-display {
  font-size: 12px;
  color: var(--text-secondary);
  font-variant-numeric: tabular-nums;
  min-width: 40px;
}

.progress-bar {
  flex: 1;
  height: 6px;
  background: var(--bg-tertiary);
  border-radius: 3px;
  overflow: hidden;
  cursor: pointer;
}

.progress-track {
  width: 100%;
  height: 100%;
  position: relative;
}

.progress-fill {
  height: 100%;
  background: var(--gradient-accent);
  border-radius: 3px;
  transition: width 0.1s ease;
}

.main-controls {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 16px;
}

.control-btn {
  background: var(--bg-secondary);
  border: 1px solid var(--border-color);
  border-radius: 50%;
  color: var(--text-primary);
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all var(--transition-fast);
  outline: none;
}

.control-btn:hover:not(:disabled) {
  background: var(--bg-tertiary);
  border-color: var(--accent-primary);
  box-shadow: var(--shadow-glow);
  transform: translateY(-2px);
}

.control-btn:active:not(:disabled) {
  transform: translateY(0);
}

.control-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.control-btn {
  width: 48px;
  height: 48px;
}

.control-btn.primary {
  width: 56px;
  height: 56px;
  background: var(--gradient-accent);
  color: var(--bg-primary);
  border-color: var(--accent-primary);
}

.control-btn.primary:hover {
  background: var(--accent-secondary);
}

.control-btn.active {
  background: var(--bg-tertiary);
  border-color: var(--accent-primary);
  color: var(--accent-primary);
}

.loop-icon-container {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
}

.loop-counter {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  font-size: 10px;
  font-weight: 700;
  color: var(--accent-primary);
  background: var(--bg-primary);
  border-radius: 50%;
  width: 16px;
  height: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.volume-controls {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-left: auto;
}

.volume-slider-container {
  overflow: hidden;
}

.volume-slider {
  width: 100%;
  height: 4px;
  background: var(--bg-tertiary);
  border-radius: 2px;
  outline: none;
  cursor: pointer;
  -webkit-appearance: none;
}

.volume-slider::-webkit-slider-thumb {
  -webkit-appearance: none;
  width: 16px;
  height: 16px;
  background: var(--accent-primary);
  border-radius: 50%;
  cursor: pointer;
  box-shadow: var(--shadow-glow);
}

.volume-slider::-moz-range-thumb {
  width: 16px;
  height: 16px;
  background: var(--accent-primary);
  border-radius: 50%;
  cursor: pointer;
  border: none;
  box-shadow: var(--shadow-glow);
}

/* Help Overlay Styles */
.help-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.8);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  backdrop-filter: blur(10px);
}

.help-content {
  background: var(--gradient-primary);
  border: 1px solid var(--border-color);
  border-radius: var(--radius-large);
  padding: 32px;
  max-width: 600px;
  max-height: 80vh;
  overflow-y: auto;
  box-shadow: var(--shadow-primary);
}

.help-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 24px;
}

.help-title-section {
  display: flex;
  align-items: center;
  gap: 16px;
}

.help-logo {
  width: 40px;
  height: 40px;
  filter: drop-shadow(0 0 8px var(--accent-glow));
}

.help-title-section h2 {
  color: var(--text-primary);
  margin: 0 0 4px 0;
  font-size: 24px;
  background: var(--gradient-accent);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.help-title-section p {
  color: var(--text-secondary);
  margin: 0;
  font-size: 14px;
}

.close-button {
  background: var(--bg-secondary);
  border: 1px solid var(--border-color);
  border-radius: 50%;
  color: var(--text-primary);
  cursor: pointer;
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all var(--transition-fast);
}

.close-button:hover {
  background: var(--bg-tertiary);
  border-color: var(--accent-primary);
  color: var(--accent-primary);
}

.help-sections {
  display: flex;
  flex-direction: column;
  gap: 24px;
  margin-bottom: 24px;
}

.help-section {
  background: var(--bg-secondary);
  border: 1px solid var(--border-color);
  border-radius: var(--radius-medium);
  padding: 20px;
}

.section-header {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 16px;
  color: var(--accent-primary);
}

.section-header h3 {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
}

.shortcuts-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.shortcut-item {
  display: flex;
  align-items: center;
  gap: 16px;
}

.shortcut-key {
  background: var(--bg-tertiary);
  border: 1px solid var(--border-color);
  border-radius: var(--radius-small);
  padding: 6px 12px;
  font-family: 'Courier New', monospace;
  font-size: 12px;
  font-weight: 600;
  color: var(--accent-primary);
  min-width: 120px;
  text-align: center;
}

.shortcut-description {
  color: var(--text-primary);
  font-size: 14px;
}

.help-footer {
  background: var(--bg-secondary);
  border: 1px solid var(--border-color);
  border-radius: var(--radius-medium);
  padding: 20px;
  margin-bottom: 24px;
}

.footer-content {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.tip {
  color: var(--text-secondary);
  font-size: 14px;
  line-height: 1.5;
}

.welcome-message {
  text-align: center;
}

.welcome-message p {
  color: var(--text-primary);
  margin: 0 0 8px 0;
  font-size: 16px;
}

.welcome-message p:last-child {
  color: var(--text-secondary);
  font-size: 14px;
}

.help-actions {
  display: flex;
  justify-content: center;
}

/* Playlist Manager Styles */
.playlist-manager-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.8);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  backdrop-filter: blur(10px);
}

.playlist-manager {
  background: var(--gradient-primary);
  border: 1px solid var(--border-color);
  border-radius: var(--radius-large);
  width: 90%;
  max-width: 800px;
  height: 80vh;
  display: flex;
  flex-direction: column;
  box-shadow: var(--shadow-primary);
}

.manager-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 24px;
  border-bottom: 1px solid var(--border-color);
}

.manager-header h2 {
  color: var(--text-primary);
  margin: 0;
  font-size: 24px;
  background: var(--gradient-accent);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.manager-tabs {
  display: flex;
  border-bottom: 1px solid var(--border-color);
}

.tab {
  flex: 1;
  background: none;
  border: none;
  padding: 16px 24px;
  color: var(--text-secondary);
  cursor: pointer;
  transition: all var(--transition-fast);
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  font-size: 14px;
  font-weight: 500;
}

.tab:hover {
  background: var(--bg-secondary);
  color: var(--text-primary);
}

.tab.active {
  background: var(--bg-secondary);
  color: var(--accent-primary);
  border-bottom: 2px solid var(--accent-primary);
}

.tab-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.add-section {
  padding: 24px;
  border-bottom: 1px solid var(--border-color);
  background: var(--bg-glass);
  backdrop-filter: blur(10px);
}

.input-group {
  display: flex;
  gap: 12px;
  margin-bottom: 16px;
}

.input-group .input {
  flex: 1;
}

.error-message {
  display: flex;
  align-items: center;
  gap: 8px;
  color: #ff6b6b;
  font-size: 14px;
  background: rgba(255, 107, 107, 0.1);
  border: 1px solid rgba(255, 107, 107, 0.3);
  border-radius: var(--radius-small);
  padding: 8px 12px;
}

.playlist-selection {
  margin-top: 16px;
}

.playlist-selection h4 {
  color: var(--text-primary);
  margin: 0 0 12px 0;
  font-size: 14px;
  font-weight: 600;
}

.checkbox-list {
  display: flex;
  flex-direction: column;
  gap: 8px;
  max-height: 120px;
  overflow-y: auto;
}

.checkbox-item {
  display: flex;
  align-items: center;
  gap: 8px;
  cursor: pointer;
  padding: 4px;
  border-radius: var(--radius-small);
  transition: background var(--transition-fast);
}

.checkbox-item:hover {
  background: var(--bg-secondary);
}

.checkbox-item input[type="checkbox"] {
  accent-color: var(--accent-primary);
}

.checkbox-item span {
  color: var(--text-primary);
  font-size: 14px;
}

.items-list {
  flex: 1;
  overflow-y: auto;
  padding: 16px 24px;
}

.playlist-item,
.mix-item {
  display: flex;
  align-items: center;
  gap: 16px;
  padding: 16px;
  background: var(--bg-secondary);
  border: 1px solid var(--border-color);
  border-radius: var(--radius-medium);
  margin-bottom: 12px;
  transition: all var(--transition-fast);
}

.playlist-item:hover,
.mix-item:hover {
  background: var(--bg-tertiary);
  border-color: var(--accent-primary);
  transform: translateY(-2px);
}

.item-thumbnail {
  width: 60px;
  height: 45px;
  border-radius: var(--radius-small);
  overflow: hidden;
  flex-shrink: 0;
}

.item-thumbnail img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.item-details {
  flex: 1;
  min-width: 0;
}

.item-details h4 {
  color: var(--text-primary);
  margin: 0 0 4px 0;
  font-size: 16px;
  font-weight: 600;
  line-height: 1.3;
}

.item-details p {
  color: var(--text-secondary);
  margin: 0 0 8px 0;
  font-size: 14px;
}

.item-meta {
  font-size: 12px;
  color: var(--text-muted);
}

.mix-playlists {
  display: flex;
  flex-wrap: wrap;
  gap: 4px;
  margin-top: 8px;
}

.mix-playlist-tag {
  background: var(--bg-tertiary);
  color: var(--text-secondary);
  font-size: 11px;
  padding: 2px 6px;
  border-radius: var(--radius-small);
  border: 1px solid var(--border-color);
}

.item-actions {
  display: flex;
  gap: 8px;
}

.btn.danger:hover {
  background: #ff6b6b;
  border-color: #ff6b6b;
  color: white;
}