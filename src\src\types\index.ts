export interface Video {
  id: string;
  title: string;
  author: string;
  duration: string;
  thumbnail: string;
  url: string;
}

export interface Playlist {
  id: string;
  title: string;
  description: string;
  thumbnail: string;
  url: string;
  videos: Video[];
  videoCount: number;
  createdAt: string;
  updatedAt: string;
}

export interface Mix {
  id: string;
  name: string;
  playlists: string[]; // Array of playlist IDs
  createdAt: string;
  updatedAt: string;
}

export interface PlayerState {
  currentVideo: Video | null;
  currentIndex: number;
  isPlaying: boolean;
  isMuted: boolean;
  volume: number;
  currentTime: number;
  duration: number;
  queue: Video[];
  history: Video[];
  loopMode: 'off' | 'single' | 'playlist';
  loopCount: number;
  shuffled: boolean;
}

export interface AppSettings {
  theme: 'dark' | 'light';
  accentColor: string;
  videoGradient: boolean;
  autoplay: boolean;
  volume: number;
  showHelp: boolean;
}

export interface SearchResult {
  videos: Video[];
  query: string;
  totalResults: number;
}

export interface SystemTrayData {
  title: string;
  progress: number;
  isPlaying: boolean;
}
